# Langfuse AI Agent Testing & Monitoring Example

This project demonstrates comprehensive AI agent testing and evaluation using Langfuse, showcasing capabilities that go beyond simple monitoring solutions like AgentOps.

## Features Demonstrated

- **AI Agent Monitoring**: Complete observability for agent workflows
- **Dataset Management**: Creating and managing test datasets
- **Experiment Tracking**: Running and comparing different agent configurations
- **Evaluation Systems**: LLM-as-a-judge and custom scoring
- **Prompt Management**: Version control and A/B testing for prompts
- **Real-world Scenario**: Customer service AI agent testing

## Project Structure

```
langfuse-agent-testing/
├── README.md
├── requirements.txt
├── .env.example
├── src/
│   ├── __init__.py
│   ├── customer_service_agent.py    # Main agent implementation
│   ├── langfuse_setup.py           # Langfuse configuration
│   ├── dataset_manager.py          # Dataset creation and management
│   ├── experiment_runner.py        # Experiment execution
│   ├── evaluators.py              # Custom evaluation functions
│   └── prompt_manager.py          # Prompt management and A/B testing
├── examples/
│   ├── basic_monitoring.py        # Basic agent monitoring
│   ├── dataset_creation.py        # Creating test datasets
│   ├── run_experiments.py         # Running experiments
│   └── evaluation_demo.py         # Evaluation examples
└── tests/
    └── test_agent.py              # Unit tests with <PERSON><PERSON>
```

## Setup Instructions

1. **Install Dependencies**
```bash
pip install -r requirements.txt
```

2. **Environment Configuration**
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. **Run Examples**
```bash
python examples/basic_monitoring.py
python examples/dataset_creation.py
python examples/run_experiments.py
```

## Key Advantages Over AgentOps

| Feature | Langfuse | AgentOps |
|---------|----------|----------|
| Testing UI | ✅ Playground, Dataset Management | ❌ No testing interface |
| Experiment Tracking | ✅ Compare configurations | ❌ Monitoring only |
| Evaluation Framework | ✅ LLM-as-judge, Custom scoring | ❌ No evaluation tools |
| Prompt Management | ✅ Version control, A/B testing | ❌ No prompt management |
| Dataset Management | ✅ Create, manage test sets | ❌ No dataset features |

This example shows how Langfuse provides a complete development and testing platform, not just monitoring.

## Quick Start

```python
from src.customer_service_agent import CustomerServiceAgent
from src.langfuse_setup import setup_langfuse

# Initialize Langfuse
langfuse = setup_langfuse()

# Create and test agent
agent = CustomerServiceAgent()
response = agent.handle_query("How do I return a product?")
print(f"Agent response: {response}")
```
